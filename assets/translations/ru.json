{"app": {"name": "<PERSON><PERSON>", "version": "Версия приложения"}, "auth": {"login": {"title": "Вход в приложение", "subtitle": "Введите номер телефона\nдля входа в приложение", "phone_label": "Номер телефона", "phone_placeholder": "+998 00 000 00 00", "phone_validation": "Введите полный номер телефона", "agree_terms": "Я согласен с условиями использования", "terms_error": "Пожалуйста, согласитесь с условиями", "demo_login": "Демо вход", "login_button": "Отправить", "send_sms": "Отправить SMS", "login_error": "Ошибка входа", "login_failed": "Вход не удался. Пожалуйста, попробуйте еще раз.", "role_supervisor": "Контролер", "role_seller": "Продавец"}, "sms_verification": {"title": "SMS подтверждение", "subtitle": "Введите код, отправленный\nна ваш номер телефона", "code_placeholder": "000000", "verify_button": "Отправить", "resend_code": "Отправить повторно", "resend_text": "Не получили код? ", "verification_error": "Ошибка подтверждения", "enter_code_title": "Введите код", "enter_code_subtitle": " отправленный на ваш номер\n6-значный SMS код", "code_validation_error": "Введите 6-значный SMS код", "sms_resent_success": "SMS код отправлен повторно", "generic_error": "Произошла ошибка", "verification_failed": "SMS код не подтвержден", "unexpected_error": "Неожиданная ошибка: ", "sending": "Отправляется...", "resend_with_countdown": "Отправить повторно ({countdown})", "resend_simple": "Отправить повторно"}, "role_selection": {"title": "Выберите роль", "subtitle": "В какой роли вы хотите\nиспользовать приложение?", "continue_button": "Продолжить", "nazoratchi": "Контролер", "nazoratchi_description": "Контролирующий сотрудник", "sotuvchi": "Продавец", "sotuvchi_description": "Занимающийся торговлей"}, "services": {"sms_sent": "SMS код отправлен", "error_occurred": "Произошла ошибка: ", "login_success": "Успешный вход", "invalid_sms_code": "Неверный SMS код", "demo_login": "Вход в демо режиме", "no_internet": "Нет интернет соединения. Пожалуйста, проверьте подключение.", "sms_resent": "SMS код отправлен повторно", "sms_send_error": "Ошибка отправки SMS кода", "server_error": "Ошибка сервера", "general_error": "Произошла ошибка", "connection_timeout": "Время соединения истекло", "receive_timeout": "Время ожидания ответа истекло", "connection_error": "Нет интернет соединения", "bad_request": "Неверные данные", "unexpected_error": "Произошла неожиданная ошибка"}}, "navigation": {"home": "Главная", "statistics": "Статистика", "market_structure": "Структура рынка", "payment_history": "История платежей", "empty_places": "Свободные места", "profile": "Профиль"}, "market_structure": {"no_pavilions_subtitle": "Пока нет доступных павильонов"}, "profile": {"men": "Мужской", "women": "Женский", "personal_info": "Личная информация", "personal_info_subtitle": "Данные профиля, должностная информация", "seller_personal_info_subtitle": "Данные профиля, прикрепленные места", "biometric_data": "Биометрические данные", "biometric_data_subtitle": "Просмотр подтвержденного лица", "notifications": "Уведомления", "notifications_subtitle": "Настройки push уведомлений", "language_selection": "Выбор языка", "language_subtitle_uzbek": "Узбекский (по умолчанию)", "language_subtitle_russian": "Русский", "logout": "Выход", "logout_subtitle": "Выйти из аккаунта", "edit_profile": "Редактировать профиль", "save_changes": "Сохранить изменения", "cancel": "Отмена", "select_image": "Выбрать изображение", "camera": "Камера", "gallery": "Галерея", "first_name": "Имя", "last_name": "Фамилия", "middle_name": "Отчество", "phone_number": "Номер телефона", "gender": "Пол", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pavilion": "Павиль<PERSON>н", "blocks": "Блоки", "places": "Места", "blocks_count": " <PERSON><PERSON><PERSON><PERSON><PERSON>", "places_count": " мест", "default_user_name": "Пользователь", "loading_info": "Загрузка информации...", "user_data_not_found": "Данные пользователя не найдены", "authentication_error": "Ошибка аутентификации", "image_selection_error": "Ошибка при выборе изображения", "logout_confirmation": "Вы действительно хотите выйти из системы?", "logout_error": "Ошибка при выходе", "profile_loaded_from_cache": "Профиль загружен из кэша", "failed_to_load_profile": "Ошибка загрузки профиля", "profile_refreshed_successfully": "Профиль успешно обновлен", "failed_to_refresh_profile": "Ошибка обновления профиля", "failed_to_load_cached_profile": "Ошибка загрузки кэшированного профиля", "profile_image_updated_successfully": "Изображение профиля успешно обновлено", "profile_image_updated": "Изображение профиля обновлено", "profile_image_update_error": "Ошибка при обновлении изображения профиля", "old_profile_image_restored": "Старое изображение профиля восстановлено", "profile_image_update_and_reload_error": "Ошибка при обновлении изображения профиля и перезагрузке", "old_profile_image_restored_from_cache": "Старое изображение профиля восстановлено из кэша"}, "language": {"dialog_title": "Выберите язык", "uzbek": "<PERSON><PERSON><PERSON><PERSON>", "russian": "Русский", "english": "English", "save": "Сохранить", "cancel": "Отмена"}, "home": {"tariff": "Тар<PERSON><PERSON>"}, "payment": {"errors": {"bad_request": "Отправлены неверные данные", "unauthorized": "Ошибка авторизации", "not_found": "Платеж не найден", "conflict": "Платеж уже существует", "server_error": "Ошибка сервера", "payment_error": "Ошибка в процессе платежа", "unexpected_error": "Произошла неожиданная ошибка"}, "types": {"cash": "Оплата наличными", "terminal": "Оплата через терминал", "qr": "Оплата через Click", "cash_short": "Наличные", "terminal_short": "<PERSON>ер<PERSON><PERSON><PERSON><PERSON>", "qr_short": "Click"}}, "places": {"empty_place": "Пустое место", "unusable_place": "Непригодное место", "needs_repair": "Требует ремонта", "dirty_place": "Грязное место", "broken_equipment": "Сломанное оборудование", "place_number": "Место", "block": "блок", "pavilion": "Свежие фрукты", "fresh_fruits": "Свежие фрукты", "no_empty_places": "Свободные места не найдены", "no_empty_places_subtitle": "В настоящее время нет свободных мест для аренды", "category": "Категория:", "rental_price": "Аренда: {price}", "contact": "Связаться", "contact_message": "Для получения подробной информации об этом месте свяжитесь с администрацией рынка.", "place_numbers": "Номера мест", "not_found": "Места не найдены", "not_found_subtitle": "В настоящее время в этом блоке нет мест", "call": "Позвонить"}, "common": {"save": "Сохранить", "cancel": "Отмена", "close": "Закрыть", "ok": "OK", "yes": "Да", "no": "Нет", "loading": "Загрузка...", "error": "Ошибка", "error_occurred": "Произошла ошибка", "success": "Успех", "retry": "Повторить", "refresh": "Обновить", "search": "Поиск", "filter": "Фильтр", "clear": "Очистить", "no_internet_connection": "Нет интернет-соединения. Пожалуйста, проверьте подключение.", "unknown_seller": "Неизвестный продавец"}, "date_picker": {"dialog_title": "Выберите дату", "ok": "OK", "cancel": "Отмена", "months": {"january": "Январь", "february": "Февраль", "march": "Ма<PERSON><PERSON>", "april": "Апрель", "may": "<PERSON><PERSON><PERSON>", "june": "Июнь", "july": "Июль", "august": "Август", "september": "Сентябрь", "october": "Октябрь", "november": "Ноябрь", "december": "Декабрь"}, "weekdays": {"monday": "Понедельник", "tuesday": "Вторник", "wednesday": "Среда", "thursday": "Четверг", "friday": "Пятница", "saturday": "Суббота", "sunday": "Воскресенье"}, "weekdays_short": {"monday": "Пн", "tuesday": "Вт", "wednesday": "Ср", "thursday": "Чт", "friday": "Пт", "saturday": "Сб", "sunday": "Вс"}}, "errors": {"network_error": "Ошибка сетевого соединения", "server_error": "Ошибка сервера", "unknown_error": "Неизвестная ошибка", "timeout_error": "Время истекло", "unauthorized": "Не авторизован", "forbidden": "Запрещено", "not_found": "Не найдено", "bad_request": "Неверный запрос", "internal_server_error": "Внутренняя ошибка сервера", "no_internet": "Нет интернет соединения", "payment_process_error": "Ошибка процесса платежа:", "no_data_available": "Данные недоступны", "connection_timeout": "Время соединения истекло. Пожалуйста, попробуйте снова.", "send_timeout": "Время отправки данных истекло. Пожалуйста, попробуйте снова.", "receive_timeout": "Время получения данных истекло. Пожалуйста, попробуйте снова.", "request_cancelled": "Запрос отменен.", "connection_error": "Ошибка соединения. Проверьте интернет соединение.", "bad_request_detailed": "Неверный запрос. Проверьте данные.", "unauthorized_detailed": "Ошибка авторизации. Войдите снова.", "forbidden_detailed": "Доступ запрещен. У вас нет прав для выполнения этого действия.", "not_found_detailed": "Данные не найдены.", "conflict": "Конфликт данных.", "validation_error": "Неверный формат данных.", "too_many_requests": "Слишком много запросов. Подождите немного.", "internal_server_detailed": "Ошибка сервера. Пожалуйста, попробуйте позже.", "bad_gateway": "Сервер не отвечает.", "service_unavailable": "Сервис временно недоступен.", "server_error_with_code": "Ошибка сервера ({code}).", "generic_error": "Неожиданная ошибка: {error}", "network_check_failed": "Нет интернет-соединения. Пожалуйста, проверьте подключение.", "network_connection_error": "Нет интернет соединения. Пожалуйста, проверьте подключение.", "demo_login_error": "Ошибка демо входа"}, "image_errors": {"unsupported_format": "Формат изображения не поддерживается", "network_error": "Нет интернет соединения", "corrupted_image": "Изображение повреждено", "image_not_found": "Изображение не найдено", "loading_error": "Ошибка загрузки изображения"}, "dialogs": {"empty_square": {"status_empty": "Пуст<PERSON>й", "description": "Это место сейчас пустое. Торговая деятельность не ведется. Открыто для аренды", "mark_as_empty": "Отметить как пустое место", "place_number": "Место #{number}"}, "contact": {"call_button": "Позвонить"}, "loading": {"default_message": "Загрузка...", "cancel": "Отмена", "hiding_dialog": "LoadingDialog: Скрытие диалога загрузки", "cannot_pop": "LoadingDialog: Невозможно закрыть - нет диалога для закрытия"}, "cash_payment": {"cancel": "Отмена", "confirm_error": "Ошибка подтверждения платежа", "process_error": "Ошибка обработки платежа: {error}", "amount_format_comment": "Извлечь число из строки суммы типа \"1 kunlik (12 000 UZS)\""}, "payment_status": {"creating": "Создание платежа...", "existing_payment_found": "Найден существующий платеж", "payment_created": "Платеж создан", "confirming": "Подтверждение платежа...", "paid": "Оплачено!", "error_occurred": "Произошла ошибка", "pending_payment_notice": "Этот платеж был создан ранее и еще не подтвержден", "unknown_error": "Неизвестная ошибка", "delete_payment": "Удалить", "accept_payment": "Прин<PERSON>л", "show_qr_code": "Показать QR код", "continue": "Продолжить", "close": "Закрыть", "retry": "Повторить", "cheque_number": "Номер чека:", "created_at": "Создан:", "days_count": "Количество дней:", "status": "Статус:", "status_pending": "Ожидание", "status_new": "Новый", "days_unit": "<PERSON><PERSON><PERSON><PERSON>", "places_label": "Места:", "delete_error": "Ошибка удаления чека", "delete_error_with_message": "Ошибка удаления чека: {error}", "creation_error": "Ошибка создания платежа", "creation_error_with_message": "Ошибка создания платежа: {error}", "confirm_error": "Ошибка подтверждения платежа", "process_error": "Ошибка обработки платежа: {error}", "unconfirmed_payment_exists": "У вас есть неподтвержденный платеж.\n\nСначала подтвердите или отмените существующий платеж, затем создайте новый.", "success_status": "success", "qr_show_button": "Показать QR код", "grid_refresh_success": "Обновление сетки после успешного платежа с blockId: {blockId}, pavilionId: {pavilionId}", "grid_refresh_error": "Ошибка обновления сетки: {error}", "date_format_comment": "Использовать поле даты из ответа API (формат: \"2025-07-16 16:34\")", "time_empty": "", "date_contains_space": " ", "date_parts_separator": " ", "date_part_index_0": "2025-07-16", "date_part_index_1": "16:34", "date_format_conversion": "Форматировать дату из \"2025-07-16\" в \"16.07.2025\"", "date_separator": "-", "formatted_date_pattern": "{day}.{month}.{year}", "formatted_datetime": "{date} {time}", "cheque_id_display": "#{id}", "days_display": "{count} д<PERSON><PERSON><PERSON>"}, "place_check": {"checking": "Проверка...", "please_wait": "Пожалуйста, подождите", "success_message": "Успешно проверено!", "error_occurred": "Произошла ошибка", "unknown_error": "Неизвестная ошибка", "max_retries_reached": "Достигнуто максимальное количество попыток. Пожалуйста, попробуйте позже.", "attempt_count": "Попытка: {current}/{max}", "cancel": "Отмена", "close": "Закрыть", "retry": "Повторить", "check_error": "Произошла ошибка при проверке", "unexpected_error": "Неожиданная ошибка: {error}"}, "qr_payment": {"title": "Оплата через Click", "place_number": "Место #{number}", "generating_qr": "Создание QR кода...", "scan_instruction": "Отсканируйте QR-код\nдля совершения платежа", "check_payment": "Проверить платеж", "retry": "Повторить", "creation_error": "Ошибка создания платежа", "creation_error_with_message": "Ошибка создания платежа: {error}", "qr_generation_error": "Ошибка создания QR кода: {error}", "payment_not_found": "Платеж не найден", "check_error": "Ошибка проверки платежа: {error}", "brightness_error": "Ошибка установки яркости: {error}", "brightness_restore_error": "Ошибка восстановления яркости: {error}", "error_message_empty": "", "service_id_comment": "Замените на ваш реальный ID сервиса", "merchant_id_comment": "Замените на ваш реальный ID мерчанта", "merchant_user_id_comment": "Замените на ваш реальный ID пользователя мерчанта"}, "terminal_payment": {"paid_button": "Оплачено", "cancel": "Отмена", "confirm_error": "Ошибка подтверждения платежа", "process_error": "Ошибка обработки платежа: {error}", "amount_format_comment": "Извлечь число из строки суммы типа \"1 kunlik (12 000 UZS)\""}, "square_dialog": {"payment_days_question": "На сколько дней хотите оплатить?", "payment_button": "Оплатить", "verified_button": "Проверено", "mark_empty_button": "Отметить как пустое место", "select_payment_method": "Выберите способ оплаты", "pay_with_click": "Оплатить через Click", "pay_with_cash": "Оплатить наличными", "pay_with_terminal": "Оплатить через терминал", "seller_placeholder": "-", "currency_uzs": "UZS", "square_meter": "м²", "debug_no_seller_payment": "Невозможно продолжить платеж: Нет действительного продавца", "debug_no_seller_cash": "Невозможно продолжить наличный платеж: Нет действительного продавца", "debug_no_seller_terminal": "Невозможно продолжить терминальный платеж: Нет действительного продавца", "demo_place_id": "demo_place_id", "debug_checking_conditions": "🔍 Проверка условий кнопки Tekshirildi:", "debug_api_mode": "   - API режим: allUnbinded={allUnbinded}, allHaveNoSeller={allHaveNoSeller}", "debug_squares_info": "   - Квадраты: {squares}", "debug_seller_details": "   - Детали продавца: {sellers}", "debug_final_result": "   - Финальный результат: {result}", "debug_legacy_mode": "   - Устаревший режим: isUnbinded={isUnbinded}, hasNoSeller={hasNoSeller}", "debug_square_info": "   - Квадрат: status={status}, seller={seller}", "debug_seller_found_grouped": "Найден ID продавца из сгруппированных квадратов: {sellerId}", "debug_seller_null_grouped": "Продавец null или пустой в сгруппированных квадратах", "debug_seller_found_single": "Найден ID продавца из одного квадрата: {sellerId}", "debug_no_seller_found": "Продавец не найден ни в сгруппированных квадратах, ни в одном квадрате", "debug_demo_seller_warning": "Предупреждение: Продавец не найден в данных места, используется демо ID продавца", "demo_user_id": "demo_user_id", "debt_amount": "{amount} UZS", "total_amount": "{amount} UZS"}}, "nazoratchi": {"reports": {"empty_square_report": {"title": "Отметить как пустое место", "image_upload": "Загрузка изображения", "image_upload_subtitle": "Загрузите фото пустого места", "description": "Написать комментарий", "submit": "Отправить", "hint_text": "Это место сейчас пустое. Торговая деятельность не ведется. Открыто для аренды", "quick_tags": {"empty_place": "Пустое место", "unusable_place": "Непригодное место", "needs_repair": "Требует ремонта", "dirty_place": "Грязное место", "broken_equipment": "Сломанное оборудование"}, "image_picker": {"select_image": "Выбрать изображение", "camera": "Камера", "gallery": "Галерея", "error_selecting_image": "Ошибка при выборе изображения", "camera_preview_message": "Пожалуйста, проверьте полученное изображение и при необходимости исправьте ориентацию"}, "success_dialog": {"title": "Отчет успешно отправлен", "message": "Ваш отчет был успешно отправлен и будет рассмотрен", "ok_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "square_selection": {"title": "Какое место вы хотите освободить"}}}, "statistics": {"all": "Общий", "plan_in_plan": "По плану", "plan_received": "Поступило", "plan_via_click": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plan_cash": "Наличные", "plan_via_terminal": "Через терминал", "plan_debt": "Задолженность", "legend_paid_places": "Оплаченные места", "legend_unpaid_places": "Неоплаченные места", "legend_unassigned_places": "Неназначенные места", "legend_empty_places": "Пустые места", "payment_delivered": "Доставлено", "payment_not_delivered": "Не доставлено"}, "payment": {"terminal_payment_title": "{amount} за {formattedAmount}\nоплачено через терминал?", "cash_payment_title": "{amount} за {formattedAmount}\nприняли?", "payment_rejected": "Платеж не принят!", "ok_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tuzilma": {"no_data_available": "Данные недоступны", "error_occurred": "Произошла ошибка", "retry": "Повторить", "payment_impossible": "Платеж невозможен", "no_seller_info": "Информация о продавце для этого места недоступна. Для совершения платежа сначала должен быть назначен продавец.", "default_category": "Свежие фрукты", "place_number": "Место #{number}", "default_seller_name": "Аза<PERSON><PERSON><PERSON><PERSON> Ахрор", "default_empty_description": "Это место в настоящее время пустое. Торговая деятельность не ведется. Открыто для аренды", "debt_loading_error": "Ошибка при загрузке данных", "status": {"paid": "Оплачено", "unpaid": "Не оплачено", "unassigned": "Не назначено", "empty": "Пуст<PERSON>й", "unknown": "Неизвестно"}, "errors": {"network_connection": "Нет интернет соединения. Пожалуйста, проверьте подключение.", "network_unavailable": "Нет интернет соединения", "pavilion_load_error": "Ошибка при загрузке данных павильона", "blocks_load_error": "Ошибка при загрузке данных блоков", "unexpected_error": "Неожиданная ошибка", "status_update_failed": "Статус не обновлен", "report_not_sent": "Отчет не отправлен", "image_file_not_found": "Файл изображения не найден"}, "success": {"empty_place_marked": "Пустое место успешно отмечено", "empty_place_mark_error": "Ошибка при отметке пустого места"}, "dialog": {"unexpected_error": "Произошла неожиданная ошибка", "place_number": "Место #{number}", "total_amount": "Общая сумма:", "total_square": "Общая площадь:", "payment_days_question": "За сколько дней хотите заплатить?", "category": "Категория:", "tariff": "Тариф:", "daily_rate": "1 день ({amount} UZS)", "place_size": "Размер места:", "place_size_meters": "{size} метр", "debt": "Задолженность", "last_payment_date": "Дата последнего платежа:", "last_payment_amount": "Сумма последнего платежа:", "debt_days": "{days} д<PERSON>ей", "seller_placeholder": "-", "currency_uzs": "UZS", "square_meter": "м²", "error_title": "Ошибка", "data_not_loaded": "Данные не загружены", "make_payment": "Совершить платеж", "checked": "Проверено", "mark_empty_place": "Отметить как пустое место", "select_payment_method": "Выберите способ оплаты", "pay_with_click": "Оплатить через Click", "pay_with_cash": "Оплатить наличными", "pay_with_terminal": "Оплатить через терминал"}}, "market_structure": {"blocks": "Блоки", "places": "Места", "total_places": "Всего мест", "category": "Категория:", "tariff": "Тариф:", "place_size": "Размер места:", "total_square": "Общая площадь:", "daily_rate": "Дневная ставка", "meter": "метр", "debt": "Задолженность", "total_amount": "Общая сумма:", "legend": {"paid": "Оплачено", "unpaid": "Не оплачено", "empty": "Пуст<PERSON>й", "unassigned": "Не назначено"}, "detailed": "Более"}}, "sotuvchi": {"home": {"title": "Главная", "tariff": "Тар<PERSON><PERSON>"}, "payment_history": {"no_payments_title": "История платежей недоступна", "no_payments_subtitle": "Пока не было совершено ни одного платежа", "place_number": "Номер места:", "tariff": "Тариф:", "paid_date": "Оплачено:"}, "profile": {"title": "Профиль", "logout_dialog_title": "Выход", "logout_dialog_content": "Вы действительно хотите выйти из системы?", "logout_dialog_cancel": "Отмена", "logout_dialog_confirm": "Выход", "image_selection_error": "Ошибка при выборе изображения", "logout_error": "Ошибка при выходе", "load_error": "Ошибка при загрузке данных профиля", "image_update_error": "Ошибка при обновлении изображения профиля", "cache_loaded": "Профиль загружен из кеша", "refresh_success": "Профиль успешно обновлен", "load_failed": "Не удалось загрузить профиль", "refresh_failed": "Не удалось обновить профиль"}, "empty_places": {"load_error": "Ошибка при загрузке данных о свободных местах", "network_error": "Нет интернет соединения. Пожалуйста, проверьте подключение.", "data_load_error": "Ошибка при загрузке данных"}, "settings": {"notifications": "Уведомления", "notifications_subtitle": "Получать уведомления о новых сообщениях", "theme": "Тема", "theme_subtitle": "Светлая или темная тема", "auto_refresh": "Автообновление", "auto_refresh_subtitle": "Автоматически обновлять данные", "theme_selection": "Тема", "theme_selection_subtitle": "Светлая или темная тема"}}, "face_control": {"errors": {"user_not_found": "Пользователь не найден", "unknown_role": "Неизвестная роль: ", "invalid_user_data": "Неверные данные пользователя", "profile_load_error": "Ошибка загрузки профиля: ", "download_error": "Ошибка загрузки", "image_capture_error": "Ошибка захвата изображения: ", "image_process_error": "Ошибка обработки изображения: ", "logout_error": "Ошибка выхода: ", "face_not_detected": "Лицо не обнаружено"}}, "camera": {"settings_title": "Настройки камеры", "hd_quality": "Высокое качество (HD)", "hd_quality_subtitle": "Улучшает качество изображения", "grid_lines": "Направляющие линии", "grid_lines_subtitle": "Для композиции изображения", "auto_save": "Автосохранение", "auto_save_subtitle": "Автоматически сохранять изображения в галерею", "image_format": "Формат изображения", "image_format_current": "Текущий: {format}", "select_image_format": "Выберите формат изображения", "jpeg_description": "Малый размер файла, хорошее качество", "png_description": "Большой размер файла, лучшее качество", "cancel": "Отмена", "gallery_selection_error": "Ошибка выбора изображения из галереи: {error}", "image_saved_to_gallery": "Изображение сохранено в галерею", "save_error": "Ошибка сохранения: {error}", "capture_error": "Ошибка захвата изображения: {error}"}, "location": {"turn_on_location": "Включить местоположение", "give_location_permission": "Разрешить местоположение", "fraud_location": "Обнаружено мошенничество с местоположением", "turn_on_location_message": "Пожалуйста, включите местоположение, чтобы приложение работало правильно!", "give_location_permission_message": "Разрешить местоположение", "fraud_location_message": "Обнаружено мошенничество с местоположением!"}, "statistics_history": {"error_occurred": "Произошла ошибка", "load_more_error": "Ошибка при загрузке дополнительных данных", "data_load_error": "Ошибка при загрузке данных", "retry": "Повторить", "no_data_found": "Данные не найдены", "places_header": "Места", "amount_header": "Сумма (UZS)", "payment_history_load_error": "Ошибка при загрузке истории платежей", "statistics_load_error": "Ошибка при загрузке статистики", "invalid_response_format": "Неверный формат ответа"}}