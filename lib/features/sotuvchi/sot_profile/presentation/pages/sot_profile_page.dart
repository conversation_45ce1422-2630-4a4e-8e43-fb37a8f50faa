import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get_storage/get_storage.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../core/utils/app_constants.dart';
import '../../../../../core/utils/jwt_decoder.dart';
import '../../../../../core/widgets/role_switcher.dart';
import '../../../../../core/widgets/universal_avatar.dart';
import '../../../../../core/widgets/universal_loading.dart';
import '../../../../../core/widgets/animated_theme_switcher.dart';
import '../../../../../core/widgets/enhanced_theme_toggle.dart';
import '../../../../../core/widgets/theme_aware_scaffold.dart';
import '../../../../../core/services/logout_service.dart';
import '../../../../../generated/assets.dart';
import '../../../../../di/dependency_injection.dart' as di;
import '../../../../auth/services/auth_service.dart';
import '../../models/sotuvchi_profile_model.dart';
import '../../../../auth/presentation/pages/login_page.dart';
import '../blocs/sot_profile_bloc.dart';
import '../blocs/sot_profile_event.dart';
import '../blocs/sot_profile_state.dart';
import '../widgets/widgets.dart';
import 'sot_personal_info_page.dart';
import '../../../../../translations/locale_keys.g.dart';
import '../../../../../core/services/language_service.dart';

class SotProfilePage extends StatefulWidget {
  final Function(UserRole)? onRoleChanged;

  const SotProfilePage({
    super.key,
    this.onRoleChanged,
  });

  @override
  State<SotProfilePage> createState() => _SotProfilePageState();
}

class _SotProfilePageState extends State<SotProfilePage> {
  File? _profileImage;
  final ImagePicker _picker = ImagePicker();
  String _selectedLanguage = LanguageService.getCurrentLanguage();
  late final AuthService _authService;
  late final SotProfileBloc _sotProfileBloc;
  late final GetStorage _storage;
  SotuvchiProfile? _userProfile;
  String? _cacheBustingTimestamp;

  @override
  void initState() {
    super.initState();
    _authService = AuthService(
      dio: di.di(),
      storage: di.di(),
      networkInfo: di.di(),
    );
    _sotProfileBloc = di.di<SotProfileBloc>();
    _storage = di.di<GetStorage>();
    _loadUserProfile();
  }

  @override
  void dispose() {
    _sotProfileBloc.close();
    super.dispose();
  }

  Future<void> _loadUserProfile() async {
    try {
      final token = _storage.read('token');
      final isDemoMode = _storage.read(is_demo) ?? false;

      String? userId;

      if (token != null) {
        // Regular authenticated user
        userId = JwtDecoder.getUserId(token);
      } else if (isDemoMode) {
        // Demo mode - extract user ID from guest token
        userId = JwtDecoder.getUserId(GUEST_TOKEN);
      }

      if (userId != null) {
        _sotProfileBloc.add(LoadSotProfileEvent(userId: userId));
      }
    } catch (e) {
      print('Error loading user profile: $e');
    }
  }

  Future<void> _onRefresh() async {
    try {
      final token = _storage.read('token');
      final isDemoMode = _storage.read(is_demo) ?? false;

      String? userId;

      if (token != null) {
        // Regular authenticated user
        userId = JwtDecoder.getUserId(token);
      } else if (isDemoMode) {
        // Demo mode - extract user ID from guest token
        userId = JwtDecoder.getUserId(GUEST_TOKEN);
      }

      if (userId != null) {
        _sotProfileBloc.add(RefreshSotProfileEvent(userId: userId));

        // Wait for the refresh to complete
        await _sotProfileBloc.stream
            .firstWhere((state) => state is! SotProfileRefreshing);
      }
    } catch (e) {
      print('Error refreshing user profile: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ThemeAwareScaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: BlocListener<SotProfileBloc, SotProfileState>(
        bloc: _sotProfileBloc,
        listener: (context, state) {
          if (state is SotProfileLoaded) {
            if (mounted) {
              setState(() {
                _userProfile = state.userProfile;
              });
            }
          } else if (state is SotProfileError) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
              // Use cached profile if available
              if (state.cachedProfile != null) {
                setState(() {
                  _userProfile = state.cachedProfile;
                });
              }
            }
          } else if (state is SotProfileImageUpdated) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: AppColors.cGreenishColor,
                ),
              );
              setState(() {
                _userProfile = state.updatedProfile;
                _profileImage =
                    null; // Clear local image since server image is updated
                _cacheBustingTimestamp =
                    DateTime.now().millisecondsSinceEpoch.toString();
              });
            }
          } else if (state is SotProfileImageUpdateFailed) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
              setState(() {
                _userProfile = state.originalProfile;
                _profileImage = null; // Revert to server image
                _cacheBustingTimestamp =
                    DateTime.now().millisecondsSinceEpoch.toString();
              });
            }
          }
        },
        child: RefreshIndicator(
          onRefresh: _onRefresh,
          color: Theme.of(context).colorScheme.primary,
          backgroundColor: Theme.of(context).colorScheme.surface,
          child: CustomScrollView(
            slivers: [
              SliverAppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                scrolledUnderElevation: 0,
                surfaceTintColor: Colors.transparent,
                centerTitle: false,
                floating: false,
                pinned: true,
                expandedHeight: 0,
                title: Text(
                  LocaleKeys.sotuvchi_profile_title.tr(),
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Theme.of(context).colorScheme.onBackground,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    _buildProfileHeader(),

                    ///For debugging
                    // if (widget.onRoleChanged != null) ...[
                    //   Padding(
                    //     padding: EdgeInsets.symmetric(horizontal: 16.w),
                    //     child: RoleSwitcher(
                    //       currentRole: UserRole.sotuvchi,
                    //       onRoleChanged: widget.onRoleChanged!,
                    //     ),
                    //   )
                    // ],
                    Gap(24.h),
                    _buildMenuSection(),
                    Gap(24.h),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Decorative semicircle background
        // Positioned(
        //   top: -710.h,
        //   left: -150.w,
        //   right: -150.w,
        //   child: Container(
        //     height: 1000.h,
        //     decoration: BoxDecoration(
        //       color: Theme.of(context).colorScheme.surface,
        //       shape: BoxShape.circle,
        //     ),
        //   ),
        // ),
        // Profile content
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            children: [
              // Profile Image with Camera Icon
              BlocBuilder<SotProfileBloc, SotProfileState>(
                bloc: _sotProfileBloc,
                builder: (context, state) {
                  // Only show local image during upload process, otherwise use server image
                  File? displayImageFile;
                  String? displayImageUrl;

                  if (state is SotProfileImageUpdating &&
                      _profileImage != null) {
                    // Show local image only during upload
                    displayImageFile = _profileImage;
                    displayImageUrl = null;
                  } else if (state is SotProfileLoaded) {
                    // Show server image URL (updated or old)
                    displayImageFile = null;
                    String? baseImageUrl = state.userProfile.image;
                    if (baseImageUrl != null &&
                        _cacheBustingTimestamp != null) {
                      // Add cache-busting parameter to force reload
                      displayImageUrl =
                          '$baseImageUrl?t=$_cacheBustingTimestamp';
                    } else {
                      displayImageUrl = baseImageUrl;
                    }
                  } else if (state is SotProfileError &&
                      state.cachedProfile != null) {
                    // Show cached server image URL on error
                    displayImageFile = null;
                    displayImageUrl = state.cachedProfile!.image;
                  }

                  return UniversalAvatar.profile(
                    key: _cacheBustingTimestamp != null
                        ? ValueKey('avatar_$_cacheBustingTimestamp')
                        : null,
                    imageFile: displayImageFile,
                    imageUrl: displayImageUrl,
                    size: 120,
                    isLoading: state is SotProfileRefreshing ||
                        state is SotProfileImageUpdating,
                    onCameraTap: _showImagePickerOptions,
                    fallbackText: state is SotProfileLoaded
                        ? state.userProfile.fullNameWithMiddle
                        : state is SotProfileError &&
                                state.cachedProfile != null
                            ? state.cachedProfile!.fullNameWithMiddle
                            : null,
                  );
                },
              ),
              Gap(24.h),
              BlocBuilder<SotProfileBloc, SotProfileState>(
                bloc: _sotProfileBloc,
                builder: (context, state) {
                  if (state is SotProfileLoaded) {
                    return SizedBox(
                      child: Column(
                        children: [
                          Text(
                            state.userProfile.fullNameWithMiddle,
                            style: AppTextStyles.titleLarge.copyWith(
                              color: Theme.of(context).colorScheme.onBackground,
                              fontWeight: FontWeight.w600,
                              fontSize: 20.sp,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Gap(8.h),
                          Text(
                            state.userProfile.formattedPhone,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                              fontSize: 12.sp,
                            ),
                          ),
                        ],
                      ),
                    );
                  } else if (state is SotProfileLoading ||
                      state is SotProfileRefreshing) {
                    return UniversalLoading.shimmer(
                      child: SizedBox(
                        child: Column(
                          children: [
                            Container(
                              height: 24.h,
                              width: 200.w,
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.onBackground,
                                borderRadius: BorderRadius.circular(4.r),
                              ),
                            ),
                            Gap(8.h),
                            Container(
                              height: 16.h,
                              width: 150.w,
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.onBackground,
                                borderRadius: BorderRadius.circular(4.r),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  } else if (state is SotProfileError &&
                      state.cachedProfile != null) {
                    return SizedBox(
                      child: Column(
                        children: [
                          Text(
                            state.cachedProfile!.fullNameWithMiddle,
                            style: AppTextStyles.titleLarge.copyWith(
                              color: Theme.of(context).colorScheme.onBackground,
                              fontWeight: FontWeight.w600,
                              fontSize: 20.sp,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Gap(8.h),
                          Text(
                            state.cachedProfile!.formattedPhone,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                              fontSize: 12.sp,
                            ),
                          ),
                        ],
                      ),
                    );
                  } else {
                    return SizedBox(
                      child: Column(
                        children: [
                          Text(
                            LocaleKeys.profile_default_user_name.tr(),
                            style: AppTextStyles.titleLarge.copyWith(
                              color: Theme.of(context).colorScheme.onBackground,
                              fontWeight: FontWeight.w600,
                              fontSize: 20.sp,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Gap(8.h),
                          Text(
                            LocaleKeys.profile_loading_info.tr(),
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                              fontSize: 12.sp,
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                },
              ),
              Gap(32.h),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMenuSection() {
    return Column(
      children: [
        _buildDivider(),
        SotProfileMenuItem(
          title: LocaleKeys.profile_personal_info.tr(),
          iconPath: Assets.iconsCircleAvatar,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SotPersonalInfoPage(
                  userProfile: _userProfile,
                ),
              ),
            );
          },
          subTitle: LocaleKeys.profile_seller_personal_info_subtitle.tr(),
        ),
        _buildDivider(),
        SotProfileMenuItem(
          title: LocaleKeys.profile_language_selection.tr(),
          iconPath: Assets.iconsGlobus,
          onTap: () {
            _showLanguageSelectionDialog();
          },
          subTitle: _selectedLanguage == 'uz'
              ? LocaleKeys.profile_language_subtitle_uzbek.tr()
              : LocaleKeys.profile_language_subtitle_russian.tr(),        ),
        _buildDivider(),
        SotProfileMenuItem(
          title: LocaleKeys.sotuvchi_settings_theme_selection.tr(),
          iconPath: Assets.iconsDarkMode,
          onTap: () {
            showModalBottomSheet(
              context: context,
              backgroundColor: Colors.transparent,
              isScrollControlled: true,
              builder: (context) => const ThemeSelectionBottomSheet(),
            );
          },
          subTitle: LocaleKeys.sotuvchi_settings_theme_selection_subtitle.tr(),
          trailing: const SimpleThemeToggle(size: 24),

        ),
        _buildDivider(),
        SotProfileMenuItem(
          title: LocaleKeys.profile_logout.tr(),
          iconPath: Assets.iconsLogout,
          onTap: () {
            _showLogoutDialog();
          },
          subTitle: LocaleKeys.profile_logout_subtitle.tr(),
          isLogout: true,
        ),
        _buildDivider()
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1,
      color: Theme.of(context).colorScheme.outline,
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Text(
          LocaleKeys.sotuvchi_profile_logout_dialog_title.tr(),
          style: AppTextStyles.titleMedium.copyWith(
            color: Theme.of(context).colorScheme.onBackground,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          LocaleKeys.sotuvchi_profile_logout_dialog_content.tr(),
          style: AppTextStyles.bodyMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              LocaleKeys.sotuvchi_profile_logout_dialog_cancel.tr(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: Text(
              LocaleKeys.sotuvchi_profile_logout_dialog_confirm.tr(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).colorScheme.onError,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showLanguageSelectionDialog() {
    String tempSelectedLanguage = _selectedLanguage;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: StatefulBuilder(
          builder: (context, setDialogState) => Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 280.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header with title
                    Container(
                      padding: EdgeInsets.fromLTRB(24.w, 20.h, 16.w, 16.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            LocaleKeys.language_dialog_title.tr(),
                            style: AppTextStyles.titleMedium.copyWith(
                              color: Theme.of(context).colorScheme.onBackground,
                              fontWeight: FontWeight.w600,
                              fontSize: 18.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Language options
                    Divider(
                      height: 1,
                      color: AppColors.cGrayBorderColor,
                    ),
                    Container(
                      padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 24.h),
                      child: Column(
                        children: [
                          _buildLanguageOptionForDialog(
                            language: LocaleKeys.language_uzbek.tr(),
                            isSelected: tempSelectedLanguage == 'uz',
                            onTap: () async {
                              setDialogState(() {
                                tempSelectedLanguage = 'uz';
                              });
                              await LanguageService.setLanguage(context, 'uz');
                              setState(() {
                                _selectedLanguage = 'uz';
                              });
                              Navigator.pop(context);
                            },
                          ),
                          Gap(16.h),
                          _buildLanguageOptionForDialog(
                            language: LocaleKeys.language_russian.tr(),
                            isSelected: tempSelectedLanguage == 'ru',
                            onTap: () async {
                              setDialogState(() {
                                tempSelectedLanguage = 'ru';
                              });
                              await LanguageService.setLanguage(context, 'ru');
                              setState(() {
                                _selectedLanguage = 'ru';
                              });
                              Navigator.pop(context);
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Gap(20.h),
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  width: 50.w,
                  height: 50.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Theme.of(context).colorScheme.surface,
                  ),
                  child: Icon(
                    Icons.close,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20.w,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageOptionForDialog({
    required String language,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Row(
          children: [
            Container(
              width: 20.w,
              height: 20.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                  width: 2.w,
                ),
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 8.w,
                        height: 8.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    )
                  : null,
            ),
            Gap(10),
            Expanded(
              child: Text(
                language,
                style: AppTextStyles.bodyLarge.copyWith(
                  color: Theme.of(context).colorScheme.onBackground,
                  fontWeight: FontWeight.w500,
                  fontSize: 16.sp,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              LocaleKeys.profile_select_image.tr(),
              style: AppTextStyles.titleMedium.copyWith(
                color: Theme.of(context).colorScheme.onBackground,
                fontWeight: FontWeight.w600,
              ),
            ),
            Gap(24.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildImagePickerOption(
                  icon: Icons.camera_alt,
                  label: LocaleKeys
                      .nazoratchi_reports_empty_square_report_image_picker_camera
                      .tr(),
                  onTap: () => _pickImage(ImageSource.camera),
                ),
                _buildImagePickerOption(
                  icon: Icons.photo_library,
                  label: LocaleKeys
                      .nazoratchi_reports_empty_square_report_image_picker_gallery
                      .tr(),
                  onTap: () => _pickImage(ImageSource.gallery),
                ),
              ],
            ),
            Gap(24.h),
          ],
        ),
      ),
    );
  }

  Widget _buildImagePickerOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 24.w),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 32.w,
            ),
            Gap(8.h),
            Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).colorScheme.onBackground,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _profileImage = File(image.path);
        });

        // Upload image to server
        final token = _storage.read('token');
        if (token != null) {
          final userId = JwtDecoder.getUserId(token);
          if (userId != null) {
            _sotProfileBloc.add(UpdateSotProfileImageEvent(
              userId: userId,
              imagePath: image.path,
            ));
          }
        }
      }

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(LocaleKeys.sotuvchi_profile_image_selection_error.tr()),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _logout() async {
    try {
      // Use LogoutService for complete logout with storage erasure
      final logoutService = di.di<LogoutService>();
      await logoutService.performCompleteLogout(
        showMessage: false, // We'll show our own message
        context: context,
      );

      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginPage()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('${LocaleKeys.sotuvchi_profile_logout_error.tr()}: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
