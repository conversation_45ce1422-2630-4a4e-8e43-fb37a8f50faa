import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../core/theme/theme_manager.dart';
import '../../../../../core/widgets/enhanced_theme_toggle.dart';
import '../../../../../core/utils/app_constants.dart';
import '../../../../../translations/locale_keys.g.dart';
import '../../../../../core/services/language_service.dart';

class SotSettingsPage extends StatefulWidget {
  const SotSettingsPage({super.key});

  @override
  State<SotSettingsPage> createState() => _SotSettingsPageState();
}

class _SotSettingsPageState extends State<SotSettingsPage> {
  String selectedLanguage = LanguageService.getCurrentLanguage();

  final List<Map<String, String>> languages = [
    {'name': 'O\'zbek', 'code': 'uz'},
    {'name': 'Русский', 'code': 'ru'},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          LocaleKeys.profile_language_selection.tr(),
          style: AppTextStyles.titleMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [
            Gap(24.h),
            _buildLanguageSection(),
            Gap(32.h),
            _buildOtherSettings(),
            Gap(24.h),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSection() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cCardsColor,
        borderRadius: BorderRadius.circular(cRadius16.r),
      ),
      child: Column(
        children: languages.asMap().entries.map((entry) {
          final index = entry.key;
          final language = entry.value;
          final isSelected = selectedLanguage == language['name'];
          
          return Column(
            children: [
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    setState(() {
                      selectedLanguage = language['name']!;
                    });
                  },
                  borderRadius: BorderRadius.circular(cRadius16.r),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            language['name']!,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        Container(
                          width: 20.w,
                          height: 20.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.onSurfaceVariant,
                              width: 2,
                            ),
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Colors.transparent,
                          ),
                          child: isSelected
                              ? Icon(
                                  Icons.check,
                                  color: Theme.of(context).colorScheme.onPrimary,
                                  size: 14.w,
                                )
                              : null,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              if (index < languages.length - 1) _buildDivider(),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildOtherSettings() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cCardsColor,
        borderRadius: BorderRadius.circular(cRadius16.r),
      ),
      child: Column(
        children: [
          _buildSettingItem(
            title: LocaleKeys.sotuvchi_settings_notifications.tr(),
            subtitle: LocaleKeys.sotuvchi_settings_notifications_subtitle.tr(),
            trailing: Switch(
              value: true,
              onChanged: (value) {
                // Handle notification toggle
              },
              activeColor: Theme.of(context).colorScheme.primary,
            ),
          ),
          _buildDivider(),
          _buildSettingItem(
            title: 'Tema',
            subtitle: 'Yorug\' yoki qorong\'u tema',
            trailing: const EnhancedThemeToggle(
              width: 50,
              height: 28,
            ),
          ),
          _buildDivider(),
          _buildSettingItem(
            title: 'Avtomatik yangilanish',
            subtitle: 'Ma\'lumotlarni avtomatik yangilash',
            trailing: Switch(
              value: false,
              onChanged: (value) {
                // Handle auto refresh toggle
              },
              activeColor: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem({
    required String title,
    required String subtitle,
    required Widget trailing,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(4.h),
                Text(
                  subtitle,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
          ),
          trailing,
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1,
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
    );
  }
}
