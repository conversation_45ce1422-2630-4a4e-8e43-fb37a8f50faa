import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get_storage/get_storage.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../core/utils/app_constants.dart';
import '../../../../../core/utils/app_functions.dart';
import '../../../../../core/utils/jwt_decoder.dart';
import '../../../../../core/widgets/universal_loading.dart';
import '../../../../../di/dependency_injection.dart' as di;
import '../../../sot_profile/models/sotuvchi_profile_model.dart';
import '../../../sot_profile/presentation/blocs/sot_profile_bloc.dart';
import '../../../sot_profile/presentation/blocs/sot_profile_event.dart';
import '../../../sot_profile/presentation/blocs/sot_profile_state.dart';
import '../../../../../translations/locale_keys.g.dart';

class SotHomePage extends StatefulWidget {
  const SotHomePage({super.key});

  @override
  State<SotHomePage> createState() => _SotHomePageState();
}

class _SotHomePageState extends State<SotHomePage> {
  late final SotProfileBloc _sotProfileBloc;
  late final GetStorage _storage;
  SotuvchiProfile? _userProfile;

  @override
  void initState() {
    super.initState();
    _sotProfileBloc = di.di<SotProfileBloc>();
    _storage = di.di<GetStorage>();
    _loadUserProfile();
  }

  @override
  void dispose() {
    _sotProfileBloc.close();
    super.dispose();
  }

  void _loadUserProfile() {
    print('SotHomePage - Loading user profile...');
    final token = _storage.read('token');
    final isDemoMode = _storage.read(is_demo) ?? false;

    String? userId;

    if (token != null) {
      // Regular authenticated user
      userId = JwtDecoder.getUserId(token);
    } else if (isDemoMode) {
      // Demo mode - extract user ID from guest token
      userId = JwtDecoder.getUserId(GUEST_TOKEN);
      print('SotHomePage - Using demo mode with guest token');
    }

    if (userId != null) {
      print(
          'SotHomePage - Dispatching LoadSotProfileEvent for userId: $userId');
      _sotProfileBloc.add(LoadSotProfileEvent(userId: userId));
    } else {
      print('SotHomePage - No userId found');
    }
  }

  Future<void> _refreshProfile() async {
    final token = _storage.read('token');
    if (token != null) {
      final userId = JwtDecoder.getUserId(token);
      if (userId != null) {
        _sotProfileBloc.add(RefreshSotProfileEvent(userId: userId));

        // Wait for the refresh to complete
        await _sotProfileBloc.stream
            .where((state) => state is! SotProfileRefreshing)
            .first;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        title: Text(
          LocaleKeys.sotuvchi_home_title.tr(),
          style: AppTextStyles.titleLarge.copyWith(
            color: Theme.of(context).colorScheme.onBackground,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: BlocListener<SotProfileBloc, SotProfileState>(
        bloc: _sotProfileBloc,
        listener: (context, state) {
          if (state is SotProfileLoaded) {
            if (mounted) {
              setState(() {
                _userProfile = state.userProfile;
              });
            }
          } else if (state is SotProfileError) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
              // Use cached profile if available
              if (state.cachedProfile != null) {
                setState(() {
                  _userProfile = state.cachedProfile;
                });
              }
            }
          }
        },
        child: RefreshIndicator(
          onRefresh: _refreshProfile,
          color: Theme.of(context).colorScheme.primary,
          backgroundColor: Theme.of(context).colorScheme.surface,
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Metrics and Pricing Section
                        _buildTopSection(),
                        Gap(24),
                        // Action Button
                        _buildActionButton(),
                        Gap(16),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildTopSection() {
    return BlocBuilder<SotProfileBloc, SotProfileState>(
      bloc: _sotProfileBloc,
      builder: (context, state) {
        // Debug: Print current state
        print('SotHomePage - Current BLoC state: ${state.runtimeType}');
        print('SotHomePage - _userProfile is null: ${_userProfile == null}');

        // Show skeleton loading for initial loading, refreshing, and when no data is available
        if (state is SotProfileLoading ||
            state is SotProfileRefreshing ||
            (state is SotProfileInitial)) {
          print('SotHomePage - Showing skeleton loading');
          return _buildTopSectionSkeleton();
        }

        print('SotHomePage - Showing actual content');

        return Container(
          padding: EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Metrics Row
              Row(
                children: [
                  Expanded(
                    child: _buildMetricItem(
                      title: LocaleKeys.places_block.tr(),
                      value: _userProfile?.blocks.isNotEmpty == true
                          ? '${_userProfile!.blocks.first.title}'
                          : '-',
                    ),
                  ),
                  Expanded(
                    child: _buildPlacesListSection(),
                  ),
                ],
              ),

              // Places Section (if multiple places)
              // if (_userProfile?.places.isNotEmpty == true &&
              //     _userProfile!.places.length > 1) ...[
              //   Gap(16),
              //   _buildPlacesListSection(),
              // ],

              // Divider
              Container(
                width: double.infinity,
                height: 1,
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                margin: EdgeInsets.symmetric(vertical: 20),
              ),

              // Pricing Section
              Text(
                LocaleKeys.sotuvchi_home_tariff.tr(),
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontSize: 14.sp,
                ),
              ),
              Gap(8),
              _buildDailyRateSection(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMetricItem({
    required String title,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: 14.sp,
          ),
        ),
        Gap(8),
        Text(
          value,
          style: AppTextStyles.headlineSmall.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
            fontSize: AppFunctions.getResponsiveFontSize(value).sp,
          ),
        ),
      ],
    );
  }

  Widget _buildPlacesListSection() {
    if (_userProfile == null || _userProfile!.places.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.places_place_numbers.tr(),
          style: AppTextStyles.bodyMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: 14.sp,
          ),
        ),
        Gap(8),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: _userProfile!.places.map((place) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                '#${place.displayTitle}',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: 14.sp,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDailyRateSection() {
    if (_userProfile == null) {
      // Show shimmer loading for daily rate
      return UniversalLoading.shimmer(
        child: Container(
          width: 150,
          height: 24,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      );
    }

    final dailyRateText = _buildDailyRateText();
    return Text(
      dailyRateText,
      style: AppTextStyles.headlineSmall.copyWith(
        color: Theme.of(context).colorScheme.onSurface,
        fontWeight: FontWeight.w600,
        fontSize: AppFunctions.getResponsiveFontSize(dailyRateText).sp,
      ),
    );
  }

  String _buildDailyRateText() {
    if (_userProfile == null || _userProfile!.places.isEmpty) {
      return '${LocaleKeys.nazoratchi_tuzilma_dialog_daily_rate.tr()} 0 UZS';
    }

    // Calculate total daily rate from all places
    int totalDailyRate = 0;
    for (var place in _userProfile!.places) {
      totalDailyRate += place.price;
    }

    return '${LocaleKeys.payment_daily_rate.tr()} ${AppFunctions.formatNumber(totalDailyRate)} UZS';
  }

  Widget _buildTopSectionSkeleton() {
    return UniversalLoading.shimmer(
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Metrics Row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 40,
                        height: 14,
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      Gap(8),
                      Container(
                        width: 30,
                        height: 20,
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 70,
                        height: 14,
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      Gap(8),
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Divider
            Container(
              width: double.infinity,
              height: 1,
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
              margin: EdgeInsets.symmetric(vertical: 20),
            ),

            // Pricing Section
            Container(
              width: 40,
              height: 14,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            Gap(8),
            Container(
              width: 180,
              height: 20,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: () {
          // Handle payment action
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(Assets.iconsClick, color: Theme.of(context).colorScheme.onPrimary, scale: 2),
            Gap(12),
            Text(
              LocaleKeys.dialogs_square_dialog_pay_with_click.tr(),
              style: AppTextStyles.buttonText.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
