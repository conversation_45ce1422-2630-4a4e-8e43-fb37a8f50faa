import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../translations/locale_keys.g.dart';

/// Payment result dialog widget (success or error)
class PaymentResultDialog extends StatelessWidget {
  final bool isSuccess;
  final String message;
  final VoidCallback? onClose;

  const PaymentResultDialog({
    super.key,
    required this.isSuccess,
    required this.message,
    this.onClose,
  });

  /// Factory constructor for success dialog
  factory PaymentResultDialog.success({
    String? message,
    VoidCallback? onClose,
  }) {
    return PaymentResultDialog(
      isSuccess: true,
      message: message ?? LocaleKeys.dialogs_payment_status_success_status.tr(),
      onClose: onClose,
    );
  }

  /// Factory constructor for error dialog
  factory PaymentResultDialog.error({
    String? message,
    VoidCallback? onClose,
  }) {
    return PaymentResultDialog(
      isSuccess: false,
      message: message ?? LocaleKeys.nazoratchi_payment_payment_rejected.tr(),
      onClose: onClose,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: _getIconBackgroundColor().withValues(alpha: 0.1),
                shape: BoxShape.circle,
                border: Border.all(
                  color: _getIconBackgroundColor(),
                  width: 3,
                ),
              ),
              child: Icon(
                _getIcon(),
                color: _getIconBackgroundColor(),
                size: 40,
              ),
            ),

            const SizedBox(height: 24),

            // Message
            Text(
              message,
              textAlign: TextAlign.center,
              style: AppTextStyles.titleMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
                height: 1.4,
              ),
            ),

            const SizedBox(height: 32),

            // Close button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onClose?.call();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: _getButtonColor(),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  LocaleKeys.nazoratchi_payment_ok_button.tr(),
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIcon() {
    return isSuccess ? Icons.check : Icons.error_outline;
  }

  Color _getIconBackgroundColor() {
    return isSuccess ? AppColors.cFirstColor : AppColors.cReddishColor;
  }

  Color _getButtonColor() {
    return isSuccess ? AppColors.cGreenishColor : AppColors.cReddishColor;
  }
}
