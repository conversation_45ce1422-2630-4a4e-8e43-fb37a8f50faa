// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: prefer_single_quotes, avoid_renaming_method_parameters, constant_identifier_names

import 'dart:ui';

import 'package:easy_localization/easy_localization.dart' show AssetLoader;

class CodegenLoader extends AssetLoader{
  const CodegenLoader();

  @override
  Future<Map<String, dynamic>?> load(String path, Locale locale) {
    return Future.value(mapLocales[locale.toString()]);
  }

  static const Map<String,dynamic> _ru = {
  "app": {
    "name": "Click Bazaar",
    "version": "Версия приложения"
  },
  "auth": {
    "login": {
      "title": "Вход в приложение",
      "subtitle": "Введите номер телефона\nдля входа в приложение",
      "phone_label": "Номер телефона",
      "phone_placeholder": "+998 00 000 00 00",
      "phone_validation": "Введите полный номер телефона",
      "agree_terms": "Я согласен с условиями использования",
      "terms_error": "Пожалуйста, согласитесь с условиями",
      "demo_login": "Демо вход",
      "login_button": "Отправить",
      "send_sms": "Отправить SMS",
      "login_error": "Ошибка входа",
      "login_failed": "Вход не удался. Пожалуйста, попробуйте еще раз.",
      "role_supervisor": "Контролер",
      "role_seller": "Продавец"
    },
    "sms_verification": {
      "title": "SMS подтверждение",
      "subtitle": "Введите код, отправленный\nна ваш номер телефона",
      "code_placeholder": "000000",
      "verify_button": "Отправить",
      "resend_code": "Отправить повторно",
      "resend_text": "Не получили код? ",
      "verification_error": "Ошибка подтверждения",
      "enter_code_title": "Введите код",
      "enter_code_subtitle": " отправленный на ваш номер\n6-значный SMS код",
      "code_validation_error": "Введите 6-значный SMS код",
      "sms_resent_success": "SMS код отправлен повторно",
      "generic_error": "Произошла ошибка",
      "verification_failed": "SMS код не подтвержден",
      "unexpected_error": "Неожиданная ошибка: ",
      "sending": "Отправляется...",
      "resend_with_countdown": "Отправить повторно ({countdown})",
      "resend_simple": "Отправить повторно"
    },
    "role_selection": {
      "title": "Выберите роль",
      "subtitle": "В какой роли вы хотите\nиспользовать приложение?",
      "continue_button": "Продолжить",
      "nazoratchi": "Контролер",
      "nazoratchi_description": "Контролирующий сотрудник",
      "sotuvchi": "Продавец",
      "sotuvchi_description": "Занимающийся торговлей"
    },
    "services": {
      "sms_sent": "SMS код отправлен",
      "error_occurred": "Произошла ошибка: ",
      "login_success": "Успешный вход",
      "invalid_sms_code": "Неверный SMS код",
      "demo_login": "Вход в демо режиме",
      "no_internet": "Нет интернет соединения. Пожалуйста, проверьте подключение.",
      "sms_resent": "SMS код отправлен повторно",
      "sms_send_error": "Ошибка отправки SMS кода",
      "server_error": "Ошибка сервера",
      "general_error": "Произошла ошибка",
      "connection_timeout": "Время соединения истекло",
      "receive_timeout": "Время ожидания ответа истекло",
      "connection_error": "Нет интернет соединения",
      "bad_request": "Неверные данные",
      "unexpected_error": "Произошла неожиданная ошибка"
    }
  },
  "navigation": {
    "home": "Главная",
    "statistics": "Статистика",
    "market_structure": "Структура рынка",
    "payment_history": "История платежей",
    "empty_places": "Свободные места",
    "profile": "Профиль"
  },
  "market_structure": {
    "title": "Структура рынка",
    "error_occurred": "Произошла ошибка",
    "no_pavilions": "Павильоны не найдены",
    "no_blocks": "Блоки недоступны",
    "no_blocks_subtitle": "В настоящее время в этом павильоне нет блоков",
    "no_pavilions_subtitle": "Пока нет доступных павильонов"
  },
  "profile": {
    "men": "Мужской",
    "women": "Женский",
    "personal_info": "Личная информация",
    "personal_info_subtitle": "Данные профиля, должностная информация",
    "seller_personal_info_subtitle": "Данные профиля, прикрепленные места",
    "biometric_data": "Биометрические данные",
    "biometric_data_subtitle": "Просмотр подтвержденного лица",
    "notifications": "Уведомления",
    "notifications_subtitle": "Настройки push уведомлений",
    "language_selection": "Выбор языка",
    "language_subtitle_uzbek": "Узбекский (по умолчанию)",
    "language_subtitle_russian": "Русский",
    "logout": "Выход",
    "logout_subtitle": "Выйти из аккаунта",
    "edit_profile": "Редактировать профиль",
    "save_changes": "Сохранить изменения",
    "cancel": "Отмена",
    "select_image": "Выбрать изображение",
    "camera": "Камера",
    "gallery": "Галерея",
    "first_name": "Имя",
    "last_name": "Фамилия",
    "middle_name": "Отчество",
    "phone_number": "Номер телефона",
    "gender": "Пол",
    "address": "Адрес",
    "pavilion": "Павильон",
    "blocks": "Блоки",
    "places": "Места",
    "blocks_count": " блоков",
    "places_count": " мест",
    "default_user_name": "Пользователь",
    "loading_info": "Загрузка информации...",
    "user_data_not_found": "Данные пользователя не найдены",
    "authentication_error": "Ошибка аутентификации",
    "image_selection_error": "Ошибка при выборе изображения",
    "logout_confirmation": "Вы действительно хотите выйти из системы?",
    "logout_error": "Ошибка при выходе",
    "profile_loaded_from_cache": "Профиль загружен из кэша",
    "failed_to_load_profile": "Ошибка загрузки профиля",
    "profile_refreshed_successfully": "Профиль успешно обновлен",
    "failed_to_refresh_profile": "Ошибка обновления профиля",
    "failed_to_load_cached_profile": "Ошибка загрузки кэшированного профиля",
    "profile_image_updated_successfully": "Изображение профиля успешно обновлено",
    "profile_image_updated": "Изображение профиля обновлено",
    "profile_image_update_error": "Ошибка при обновлении изображения профиля",
    "old_profile_image_restored": "Старое изображение профиля восстановлено",
    "profile_image_update_and_reload_error": "Ошибка при обновлении изображения профиля и перезагрузке",
    "old_profile_image_restored_from_cache": "Старое изображение профиля восстановлено из кэша"
  },
  "language": {
    "dialog_title": "Выберите язык",
    "uzbek": "O'zbek",
    "russian": "Русский",
    "english": "English",
    "save": "Сохранить",
    "cancel": "Отмена"
  },
  "home": {
    "tariff": "Тариф"
  },
  "payment": {
    "daily_rate": "Дневная ставка",
    "daily_tariff": "Дневной тариф",
    "category": "Категория",
    "tariff": "Тариф",
    "place_size": "Размер места",
    "total_square": "Общая площадь",
    "debt": "Задолженность",
    "payment_success": "Платеж успешно\nпринят!",
    "payment_error": "Ошибка платежа",
    "cash_payment_title": "за {amount}\nприняли?",
    "accept_payment": "Принял",
    "payment_creation_error": "Ошибка создания платежа",
    "payment_confirmation_error": "Ошибка подтверждения платежа",
    "payment_process_error": "Ошибка процесса платежа",
    "errors": {
      "bad_request": "Отправлены неверные данные",
      "unauthorized": "Ошибка авторизации",
      "not_found": "Платеж не найден",
      "conflict": "Платеж уже существует",
      "server_error": "Ошибка сервера",
      "payment_error": "Ошибка в процессе платежа",
      "unexpected_error": "Произошла неожиданная ошибка"
    },
    "types": {
      "cash": "Оплата наличными",
      "terminal": "Оплата через терминал",
      "qr": "Оплата через Click",
      "cash_short": "Наличные",
      "terminal_short": "Терминал",
      "qr_short": "Click"
    }
  },
  "places": {
    "empty_place": "Пустое место",
    "unusable_place": "Непригодное место",
    "needs_repair": "Требует ремонта",
    "dirty_place": "Грязное место",
    "broken_equipment": "Сломанное оборудование",
    "place_number": "Место",
    "block": "блок",
    "pavilion": "Свежие фрукты",
    "fresh_fruits": "Свежие фрукты",
    "no_empty_places": "Свободные места не найдены",
    "no_empty_places_subtitle": "В настоящее время нет свободных мест для аренды",
    "category": "Категория:",
    "rental_price": "Цена аренды:",
    "contact": "Связаться",
    "contact_message": "Для получения подробной информации об этом месте свяжитесь с администрацией рынка.",
    "place_numbers": "Номера мест",
    "not_found": "Места не найдены",
    "not_found_subtitle": "В настоящее время в этом блоке нет мест",
    "call": "Позвонить"
  },
  "common": {
    "save": "Сохранить",
    "cancel": "Отмена",
    "close": "Закрыть",
    "ok": "OK",
    "yes": "Да",
    "no": "Нет",
    "loading": "Загрузка...",
    "error": "Ошибка",
    "error_occurred": "Произошла ошибка",
    "success": "Успех",
    "retry": "Повторить",
    "refresh": "Обновить",
    "search": "Поиск",
    "filter": "Фильтр",
    "clear": "Очистить",
    "no_internet_connection": "Нет интернет-соединения. Пожалуйста, проверьте подключение.",
    "unknown_seller": "Неизвестный продавец"
  },
  "face_control": {
    "title": "Система контроля лица",
    "face_control_title": "Контроль лица",
    "enter_face": "Ввести лицо",
    "confirm_face": "Подтвердить лицо",
    "settings": "Настройки",
    "about": "О программе",
    "refresh": "Обновить",
    "please_refresh_page": "Пожалуйста, обновите страницу",
    "warning_message": "Внимание! Для хорошего результата следуйте следующим рекомендациям:",
    "warning_details": "• Находиться в хорошо освещенном месте\n• На изображении должно быть только одно лицо\n• Убрать очки, маски и все аксессуары, закрывающие лицо",
    "camera_preview_message": "Пожалуйста, проверьте полученное изображение, при необходимости исправьте ориентацию. Ваше лицо должно быть прямо!",
    "uploaded": "Загружено",
    "recognised": "Распознано",
    "face_match": "Совпадение лица",
    "liveness": "Живость",
    "analyzing": "Анализируется...",
    "error_uploading": "Ошибка при загрузке",
    "leader_not_confirmed": "Требуется подтверждение администратора",
    "picture_uploaded": "Изображение загружено",
    "errors": {
      "check_internet": "Проверьте интернет-соединение",
      "user_not_found": "Информация о пользователе не найдена",
      "unknown_role": "Неизвестная роль пользователя: ",
      "invalid_user_data": "Неверные данные пользователя",
      "profile_load_error": "Ошибка загрузки профиля: ",
      "download_error": "Не удалось загрузить данные, FACE_IMAGE неверен!",
      "image_capture_error": "Ошибка захвата изображения: ",
      "image_process_error": "Ошибка обработки изображения: ",
      "logout_error": "Ошибка при выходе: ",
      "face_not_detected": "Лицо не обнаружено"
    }
  }
};
